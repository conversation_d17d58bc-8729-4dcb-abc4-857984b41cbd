<template>
	<view class="price-detail-container">
		<!-- 价格明细标题 -->
		<view class="price-detail-title">价格明细</view>
		
		<!-- 商品金额 -->
		<view class="price-item">
			<text class="price-label">商品金额</text>
			<text class="price-value">¥{{ productAmount }}</text>
		</view>
		
		<!-- 运费 -->
		<view class="price-item">
			<text class="price-label">运费</text>
			<text class="price-value">¥{{ shippingFee }}</text>
		</view>
		
		<!-- 债权抵扣 -->
		<view class="price-item-with-icon">
			<view class="price-item-left">
				<image class="price-icon" :src="debtIcon" mode="aspectFit"></image>
				<text class="price-label-small">债权抵扣</text>
			</view>
			<view class="price-item-right">
				<text class="price-value-negative">-¥{{ debtDeduction }}</text>
				<image class="more-icon" :src="moreIcon" mode="aspectFit" @tap="onDebtMore"></image>
			</view>
		</view>
		
		<!-- 现金券 -->
		<view class="price-item-with-icon">
			<view class="price-item-left">
				<image class="price-icon" :src="cashIcon" mode="aspectFit"></image>
				<text class="price-label-small">现金券</text>
			</view>
			<view class="price-item-right">
				<text class="price-value-negative">-¥{{ cashCoupon }}</text>
				<image class="more-icon" :src="moreIcon" mode="aspectFit" @tap="onCashMore"></image>
			</view>
		</view>
		
		<!-- 佣金支付 -->
		<view class="price-item-with-icon">
			<view class="price-item-left">
				<image class="price-icon" :src="commissionIcon" mode="aspectFit"></image>
				<text class="price-label-small">佣金支付</text>
			</view>
			<view class="price-item-right">
				<text class="price-value-negative">-¥{{ commissionPayment }}</text>
				<image class="more-icon" :src="moreIcon" mode="aspectFit" @tap="onCommissionMore"></image>
			</view>
		</view>
		
		<!-- 添星积分 -->
		<view class="price-item-with-icon">
			<view class="price-item-left">
				<image class="price-icon" :src="pointsIcon" mode="aspectFit"></image>
				<text class="price-label-small">添星积分</text>
			</view>
			<view class="price-item-right">
				<text class="price-value-negative">-¥{{ pointsDeduction }}</text>
				<image class="more-icon" :src="moreIcon" mode="aspectFit" @tap="onPointsMore"></image>
			</view>
		</view>
		
		<!-- 合计 -->
		<view class="price-item total-item">
			<text class="price-label">合计</text>
			<text class="price-value">¥{{ totalAmount }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PriceDetail',
	props: {
		// 商品金额
		productAmount: {
			type: [String, Number],
			default: '1924.50'
		},
		// 运费
		shippingFee: {
			type: [String, Number],
			default: '3.50'
		},
		// 债权抵扣
		debtDeduction: {
			type: [String, Number],
			default: '0.00'
		},
		// 现金券
		cashCoupon: {
			type: [String, Number],
			default: '0.00	'
		},
		// 佣金支付
		commissionPayment: {
			type: [String, Number],
			default: '0.00'
		},
		// 添星积分
		pointsDeduction: {
			type: [String, Number],
			default: '0.00'
		},
		// 合计
		totalAmount: {
			type: [String, Number],
			default: '0.00'
		}
	},
	data() {
		return {
			// 从 Figma 设计稿获取的图标资源
			debtIcon: 'http://localhost:3845/assets/b9df48f1486227e5ae2d14ed25600a14ea8196d2.svg',
			cashIcon: 'http://localhost:3845/assets/824627e3a40c4e101e6a4edac6bea64f5f5a9992.svg',
			commissionIcon: 'http://localhost:3845/assets/8e7cce4ba31a2acc0b5bfaf03a088d1cbff1ed3d.svg',
			pointsIcon: 'http://localhost:3845/assets/1303cbf8b7b3f7e5156b38b4d9bd5309f5a3c2b0.svg',
			moreIcon: 'http://localhost:3845/assets/c5a27f943631904089ff0a98b5f42bfa39aa8ce2.svg'
		}
	},
	methods: {
		// 债权抵扣更多按钮点击事件
		onDebtMore() {
			this.$emit('debt-more');
		},
		// 现金券更多按钮点击事件
		onCashMore() {
			this.$emit('cash-more');
		},
		// 佣金支付更多按钮点击事件
		onCommissionMore() {
			this.$emit('commission-more');
		},
		// 添星积分更多按钮点击事件
		onPointsMore() {
			this.$emit('points-more');
		}
	}
}
</script>

<style lang="scss" scoped>
.price-detail-container {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 28rpx 24rpx;
	// margin: 24rpx;
}

.price-detail-title {
	font-family: 'PingFang SC', sans-serif;
	font-weight: 500;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 40rpx;
}

.price-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
	height: 40rpx;
}

.price-item-with-icon {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
	height: 42rpx;
}

.price-item-left {
	display: flex;
	align-items: center;
}

.price-item-right {
	display: flex;
	align-items: center;
}

.price-label {
	font-family: 'PingFang SC', sans-serif;
	font-weight: 500;
	font-size: 28rpx;
	color: #333333;
}

.price-label-small {
	font-family: 'PingFang SC', sans-serif;
	font-weight: 500;
	font-size: 24rpx;
	color: #333333;
	margin-left: 16rpx;
}

.price-value {
	font-family: 'PingFang SC', sans-serif;
	font-weight: 600;
	font-size: 28rpx;
	color: #ff0000;
}

.price-value-negative {
	font-family: 'PingFang SC', sans-serif;
	font-weight: 600;
	font-size: 28rpx;
	color: #ff0000;
	margin-right: 16rpx;
}

.price-icon {
	width: 28rpx;
	height: 28rpx;
}

.more-icon {
	width: 20rpx;
	height: 20rpx;
}

.total-item {
	border-top: 2rpx solid #f5f5f5;
	padding-top: 24rpx;
	margin-top: 16rpx;
	margin-bottom: 0;
}
</style>
