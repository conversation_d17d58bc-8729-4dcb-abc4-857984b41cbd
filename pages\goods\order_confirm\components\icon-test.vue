<template>
	<view class="icon-test-container">
		<view class="test-title">图标测试页面</view>
		
		<view class="icon-item">
			<text>债权抵扣图标：</text>
			<image class="test-icon" src="/static/images/icons/debt-icon.svg" mode="aspectFit"></image>
		</view>
		
		<view class="icon-item">
			<text>现金券图标：</text>
			<image class="test-icon" src="/static/images/icons/cash-icon.svg" mode="aspectFit"></image>
		</view>
		
		<view class="icon-item">
			<text>佣金支付图标：</text>
			<image class="test-icon" src="/static/images/icons/commission-icon.svg" mode="aspectFit"></image>
		</view>
		
		<view class="icon-item">
			<text>添星积分图标：</text>
			<image class="test-icon" src="/static/images/icons/points-icon.svg" mode="aspectFit"></image>
		</view>
		
		<view class="icon-item">
			<text>更多按钮图标：</text>
			<image class="test-icon" src="/static/images/icons/more-icon.svg" mode="aspectFit"></image>
		</view>
	</view>
</template>

<script>
export default {
	name: 'IconTest'
}
</script>

<style lang="scss" scoped>
.icon-test-container {
	padding: 40rpx;
	background: #fff;
}

.test-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 40rpx;
	text-align: center;
}

.icon-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 20rpx;
	border: 1px solid #eee;
	border-radius: 8rpx;
}

.test-icon {
	width: 48rpx;
	height: 48rpx;
	margin-left: 20rpx;
}
</style>
